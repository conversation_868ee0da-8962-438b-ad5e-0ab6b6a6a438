/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { TaskItem } from '../types.js';
import { GeminiClient } from 'fumbly-cli-core';

/**
 * Internal task generation service that uses the LLM to dynamically create
 * contextual task breakdowns from user prompts, similar to <PERSON>'s behavior.
 * This is for internal LLM tracking, not user-facing features.
 */
export class TaskGenerator {
  private static instance: TaskGenerator;
  private geminiClient: GeminiClient | null = null;

  private constructor() {}

  static getInstance(): TaskGenerator {
    if (!TaskGenerator.instance) {
      TaskGenerator.instance = new TaskGenerator();
    }
    return TaskGenerator.instance;
  }

  /**
   * Set the Gemini client for LLM-powered task generation
   */
  setGeminiClient(client: GeminiClient) {
    this.geminiClient = client;
  }

  /**
   * Uses the LLM to analyze a user prompt and generate specific, contextual tasks.
   * This creates real, relevant tasks based on the actual request content.
   */
  async generateTasksFromPrompt(userPrompt: string): Promise<{
    title: string;
    tasks: TaskItem[];
  }> {
    if (!this.geminiClient) {
      // Fallback to simple parsing if no LLM available
      return this.generateFallbackTasks(userPrompt);
    }

    try {
      const taskGenerationPrompt = this.buildTaskGenerationPrompt(userPrompt);

      // Use the LLM to generate contextual tasks
      const response = await this.callLLMForTasks(taskGenerationPrompt);

      return this.parseTasksFromLLMResponse(response);
    } catch (error) {
      console.warn('Failed to generate tasks with LLM, using fallback:', error);
      return this.generateFallbackTasks(userPrompt);
    }
  }

  /**
   * Builds a prompt for the LLM to generate specific tasks based on user request
   */
  private buildTaskGenerationPrompt(userPrompt: string): string {
    return `Based on this user request: "${userPrompt}"

Please generate a specific, actionable task breakdown. Respond with ONLY a JSON object in this exact format:
{
  "title": "Brief title describing the work",
  "tasks": [
    "Specific task 1 based on the actual request",
    "Specific task 2 based on the actual request",
    "Specific task 3 based on the actual request"
  ]
}

Make the tasks:
- Specific to the actual request (not generic)
- Actionable and concrete
- In logical order of execution
- Between 3-8 tasks total
- Focused on the actual deliverable mentioned

Do not include generic tasks like "understand requirements" - make them specific to what the user actually asked for.`;
  }

  /**
   * Calls the LLM to generate tasks
   */
  private async callLLMForTasks(prompt: string): Promise<string> {
    if (!this.geminiClient) {
      throw new Error('No Gemini client available');
    }

    try {
      // Use the chat API to get response
      const response = await this.geminiClient.getChat().sendMessage(
        { message: prompt },
        `task-gen-${Date.now()}`
      );

      // Extract text from the response
      const responseText = response.text || '';
      return responseText.trim();
    } catch (error) {
      console.error('Error calling LLM for task generation:', error);
      throw error;
    }
  }

  /**
   * Parses the LLM response to extract tasks
   */
  private parseTasksFromLLMResponse(response: string): {
    title: string;
    tasks: TaskItem[];
  } {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      if (!parsed.title || !Array.isArray(parsed.tasks)) {
        throw new Error('Invalid JSON structure');
      }

      const tasks: TaskItem[] = parsed.tasks.map((taskText: string) => ({
        id: this.generateId(),
        text: taskText,
        completed: false,
      }));

      return {
        title: parsed.title,
        tasks,
      };
    } catch (error) {
      console.warn('Failed to parse LLM response for tasks:', error);
      // Return a fallback structure
      return {
        title: 'Task Breakdown',
        tasks: [
          { id: this.generateId(), text: 'Complete the requested work', completed: false }
        ]
      };
    }
  }

  /**
   * Fallback method when LLM is not available - creates basic tasks
   */
  private generateFallbackTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    // Extract key words to make slightly more contextual fallback
    const prompt = userPrompt.toLowerCase();
    let title = 'Task Breakdown';
    let tasks: string[] = [];

    if (prompt.includes('create') || prompt.includes('build') || prompt.includes('make')) {
      title = 'Creation Tasks';
      tasks = [
        'Plan the structure and requirements',
        'Set up the basic framework',
        'Implement core functionality',
        'Add styling and polish',
        'Test and refine'
      ];
    } else if (prompt.includes('fix') || prompt.includes('bug') || prompt.includes('error')) {
      title = 'Bug Fix Tasks';
      tasks = [
        'Identify and reproduce the issue',
        'Analyze the root cause',
        'Implement the fix',
        'Test the solution'
      ];
    } else if (prompt.includes('analyze') || prompt.includes('review') || prompt.includes('explain')) {
      title = 'Analysis Tasks';
      tasks = [
        'Examine the code/system',
        'Identify key components',
        'Analyze relationships and patterns',
        'Provide detailed explanation'
      ];
    } else {
      tasks = [
        'Understand the requirements',
        'Plan the approach',
        'Execute the solution',
        'Review and validate'
      ];
    }

    return {
      title,
      tasks: tasks.map(taskText => ({
        id: this.generateId(),
        text: taskText,
        completed: false,
      }))
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }

  /**
   * Updates a task's completion status
   */
  updateTaskStatus(tasks: TaskItem[], taskId: string, completed: boolean): TaskItem[] {
    return tasks.map(task => 
      task.id === taskId ? { ...task, completed } : task
    );
  }

  /**
   * Gets the next incomplete task
   */
  getNextTask(tasks: TaskItem[]): TaskItem | null {
    return tasks.find(task => !task.completed) || null;
  }

  /**
   * Marks the current task as complete and returns the next task
   */
  completeCurrentTask(tasks: TaskItem[]): { 
    updatedTasks: TaskItem[]; 
    nextTask: TaskItem | null; 
  } {
    const currentTask = this.getNextTask(tasks);
    if (!currentTask) {
      return { updatedTasks: tasks, nextTask: null };
    }

    const updatedTasks = this.updateTaskStatus(tasks, currentTask.id, true);
    const nextTask = this.getNextTask(updatedTasks);
    
    return { updatedTasks, nextTask };
  }
}
