/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { TaskItem } from '../types.js';

/**
 * Internal task generation service for LLM to automatically create
 * task breakdowns from user prompts, similar to <PERSON>'s behavior.
 * This is for internal LLM tracking, not user-facing features.
 */
export class TaskGenerator {
  private static instance: TaskGenerator;

  private constructor() {}

  static getInstance(): TaskGenerator {
    if (!TaskGenerator.instance) {
      TaskGenerator.instance = new TaskGenerator();
    }
    return TaskGenerator.instance;
  }

  /**
   * Analyzes a user prompt and generates relevant tasks automatically.
   * This mimics Claude Code's internal task generation behavior.
   */
  generateTasksFromPrompt(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    const prompt = userPrompt.toLowerCase().trim();
    
    // Detect the type of request and generate appropriate tasks
    if (this.isCodeCreationRequest(prompt)) {
      return this.generateCodeCreationTasks(userPrompt);
    } else if (this.isFeatureImplementationRequest(prompt)) {
      return this.generateFeatureImplementationTasks(userPrompt);
    } else if (this.isBugFixRequest(prompt)) {
      return this.generateBugFixTasks(userPrompt);
    } else if (this.isAnalysisRequest(prompt)) {
      return this.generateAnalysisTasks(userPrompt);
    } else if (this.isDocumentationRequest(prompt)) {
      return this.generateDocumentationTasks(userPrompt);
    } else if (this.isRefactoringRequest(prompt)) {
      return this.generateRefactoringTasks(userPrompt);
    } else {
      return this.generateGenericTasks(userPrompt);
    }
  }

  private isCodeCreationRequest(prompt: string): boolean {
    const keywords = [
      'create', 'build', 'make', 'develop', 'write', 'implement',
      'app', 'application', 'component', 'function', 'class', 'module'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private isFeatureImplementationRequest(prompt: string): boolean {
    const keywords = [
      'add feature', 'implement feature', 'new feature', 'feature',
      'functionality', 'capability', 'enhancement'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private isBugFixRequest(prompt: string): boolean {
    const keywords = [
      'fix', 'bug', 'error', 'issue', 'problem', 'broken', 'not working'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private isAnalysisRequest(prompt: string): boolean {
    const keywords = [
      'analyze', 'review', 'examine', 'check', 'investigate', 'understand',
      'explain', 'how does', 'what is', 'why'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private isDocumentationRequest(prompt: string): boolean {
    const keywords = [
      'document', 'documentation', 'readme', 'comment', 'explain',
      'guide', 'tutorial', 'help'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private isRefactoringRequest(prompt: string): boolean {
    const keywords = [
      'refactor', 'improve', 'optimize', 'clean up', 'restructure',
      'reorganize', 'simplify'
    ];
    return keywords.some(keyword => prompt.includes(keyword));
  }

  private generateCodeCreationTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Code Creation Tasks',
      tasks: [
        { id: this.generateId(), text: 'Analyze requirements and plan structure', completed: false },
        { id: this.generateId(), text: 'Set up project files and directories', completed: false },
        { id: this.generateId(), text: 'Implement core functionality', completed: false },
        { id: this.generateId(), text: 'Add error handling and validation', completed: false },
        { id: this.generateId(), text: 'Write tests and documentation', completed: false },
        { id: this.generateId(), text: 'Review and refine implementation', completed: false }
      ]
    };
  }

  private generateFeatureImplementationTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Feature Implementation Tasks',
      tasks: [
        { id: this.generateId(), text: 'Understand existing codebase context', completed: false },
        { id: this.generateId(), text: 'Design feature architecture', completed: false },
        { id: this.generateId(), text: 'Implement feature components', completed: false },
        { id: this.generateId(), text: 'Integrate with existing systems', completed: false },
        { id: this.generateId(), text: 'Test feature functionality', completed: false },
        { id: this.generateId(), text: 'Update documentation', completed: false }
      ]
    };
  }

  private generateBugFixTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Bug Fix Tasks',
      tasks: [
        { id: this.generateId(), text: 'Reproduce and understand the issue', completed: false },
        { id: this.generateId(), text: 'Identify root cause', completed: false },
        { id: this.generateId(), text: 'Implement fix', completed: false },
        { id: this.generateId(), text: 'Test fix thoroughly', completed: false },
        { id: this.generateId(), text: 'Verify no regressions introduced', completed: false }
      ]
    };
  }

  private generateAnalysisTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Analysis Tasks',
      tasks: [
        { id: this.generateId(), text: 'Gather relevant code and context', completed: false },
        { id: this.generateId(), text: 'Analyze code structure and patterns', completed: false },
        { id: this.generateId(), text: 'Identify key components and relationships', completed: false },
        { id: this.generateId(), text: 'Provide detailed explanation', completed: false }
      ]
    };
  }

  private generateDocumentationTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Documentation Tasks',
      tasks: [
        { id: this.generateId(), text: 'Review existing code and functionality', completed: false },
        { id: this.generateId(), text: 'Structure documentation outline', completed: false },
        { id: this.generateId(), text: 'Write comprehensive documentation', completed: false },
        { id: this.generateId(), text: 'Add examples and usage instructions', completed: false },
        { id: this.generateId(), text: 'Review and refine documentation', completed: false }
      ]
    };
  }

  private generateRefactoringTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Refactoring Tasks',
      tasks: [
        { id: this.generateId(), text: 'Analyze current code structure', completed: false },
        { id: this.generateId(), text: 'Identify improvement opportunities', completed: false },
        { id: this.generateId(), text: 'Plan refactoring approach', completed: false },
        { id: this.generateId(), text: 'Implement refactoring changes', completed: false },
        { id: this.generateId(), text: 'Test refactored code', completed: false },
        { id: this.generateId(), text: 'Verify functionality preserved', completed: false }
      ]
    };
  }

  private generateGenericTasks(userPrompt: string): {
    title: string;
    tasks: TaskItem[];
  } {
    return {
      title: 'Task Breakdown',
      tasks: [
        { id: this.generateId(), text: 'Understand the request', completed: false },
        { id: this.generateId(), text: 'Gather necessary information', completed: false },
        { id: this.generateId(), text: 'Plan approach and implementation', completed: false },
        { id: this.generateId(), text: 'Execute the solution', completed: false },
        { id: this.generateId(), text: 'Review and validate results', completed: false }
      ]
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * Updates a task's completion status
   */
  updateTaskStatus(tasks: TaskItem[], taskId: string, completed: boolean): TaskItem[] {
    return tasks.map(task => 
      task.id === taskId ? { ...task, completed } : task
    );
  }

  /**
   * Gets the next incomplete task
   */
  getNextTask(tasks: TaskItem[]): TaskItem | null {
    return tasks.find(task => !task.completed) || null;
  }

  /**
   * Marks the current task as complete and returns the next task
   */
  completeCurrentTask(tasks: TaskItem[]): { 
    updatedTasks: TaskItem[]; 
    nextTask: TaskItem | null; 
  } {
    const currentTask = this.getNextTask(tasks);
    if (!currentTask) {
      return { updatedTasks: tasks, nextTask: null };
    }

    const updatedTasks = this.updateTaskStatus(tasks, currentTask.id, true);
    const nextTask = this.getNextTask(updatedTasks);
    
    return { updatedTasks, nextTask };
  }
}
