# Automatic Task Generation Feature

## Overview

Fumbly CLI now includes an **internal automatic task generation feature** similar to Claude <PERSON>'s behavior. This feature is designed for the LLM to automatically create and track task breakdowns for each user prompt, helping organize and track progress on complex requests.

## How It Works

### Automatic Generation
- When a user submits a prompt, the system automatically analyzes the request
- Based on the type of request (code creation, bug fix, analysis, etc.), it generates relevant subtasks
- Tasks are displayed immediately after the user prompt in a structured format

### Task Types Detected
The system recognizes several types of requests and generates appropriate tasks:

1. **Code Creation**: Creating new applications, components, or modules
2. **Feature Implementation**: Adding new functionality to existing code
3. **Bug Fixes**: Identifying and resolving issues
4. **Analysis**: Understanding and explaining code
5. **Documentation**: Creating guides, comments, or README files
6. **Refactoring**: Improving code structure and organization

### Display Format
Tasks are displayed in a tree structure similar to Claude Code:

```
● Task Breakdown
  □ Understand the request
  □ Gather necessary information
  □ Plan approach and implementation
  □ Execute the solution
  □ Review and validate results
```

## Implementation Details

### Core Components

1. **TaskGenerator Service** (`packages/cli/src/ui/services/taskGenerator.ts`)
   - Analyzes user prompts and generates appropriate tasks
   - Provides different task templates based on request type
   - Manages task state and completion tracking

2. **TasksMessage Component** (`packages/cli/src/ui/components/messages/TasksMessage.tsx`)
   - Renders the task list in the CLI interface
   - Shows checkboxes and task completion status
   - Uses consistent styling with the rest of the UI

3. **useTaskGeneration Hook** (`packages/cli/src/ui/hooks/useTaskGeneration.ts`)
   - Manages task generation lifecycle
   - Integrates with the history management system
   - Provides methods for updating task progress

### Integration Points

The feature is integrated into the main chat flow in `useGeminiStream.ts`:
- Tasks are generated immediately after user messages are added to history
- Works for both normal queries and @-commands
- Runs automatically without user intervention

### Type Definitions

New types added to support the feature:
- `TaskItem`: Individual task with ID, text, and completion status
- `HistoryItemTasks`: History item type for task lists
- `MessageType.TASKS`: Enum value for task message type

## Internal LLM Feature

This is an **internal feature for the LLM only** - it's designed to help the AI assistant:
- Track progress on complex multi-step requests
- Organize work into logical subtasks
- Provide structure for handling user requests
- Maintain context across different parts of a solution

The feature is not user-facing in terms of interaction - users cannot manually check off tasks or modify them. The tasks serve as an internal organizational tool for the LLM to better manage and track its work.

## Future Enhancements

Potential improvements that could be added:
1. Task persistence across sessions
2. Progress indicators showing completion percentage
3. More sophisticated task generation based on codebase context
4. Integration with actual work completion tracking
5. Hierarchical subtasks for complex requests

## Configuration

The feature is enabled by default and requires no configuration. It automatically activates for all user prompts that are sent to the LLM for processing.
