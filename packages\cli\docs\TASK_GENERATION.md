# Automatic Task Generation Feature

## Overview

Fumbly CLI now includes an **internal automatic task generation feature** similar to <PERSON>'s behavior. This feature uses the **actual LLM** to dynamically create specific, contextual task breakdowns for each user prompt, helping organize and track progress on complex requests.

## How It Works

### LLM-Powered Generation
- When a user submits a prompt, the system sends a specialized prompt to the LLM
- The LLM analyzes the specific request and generates relevant, actionable subtasks
- Tasks are **contextual and specific** to the actual user request, not generic templates
- Tasks are displayed immediately after the user prompt in a structured format

### Dynamic Task Creation
The LLM creates tasks that are:
- **Specific to the actual request** (not generic)
- **Actionable and concrete**
- **In logical order of execution**
- **Between 3-8 tasks total**
- **Focused on the actual deliverable mentioned**

For example, for "create a mockup for a dashboard for a service like stripe in html...use tailwindcss if needed":
```
● Stripe-like Dashboard Creation
  □ Create main HTML structure with sidebar navigation
  □ Design stats cards with key metrics (revenue, transactions, customers)
  □ Implement interactive charts using Chart.js
  □ Add recent transactions feed with status indicators
  □ Create payment methods breakdown section
  □ Style with Tailwind CSS (gradients, shadows, hover effects)
  □ Add responsive grid layout for different screen sizes
  □ Implement smooth animations and transitions
```

### Display Format
Tasks are displayed in a tree structure similar to Claude Code:

```
● I'll create a comprehensive Stripe-like payment dashboard mockup with modern
  design elements and interactive features.

● Update Tasks
  ├ □ Create main HTML structure with sidebar navigation
  ├ □ Design stats cards with key metrics (revenue, transactions, customers)
  ├ □ Implement interactive charts using Chart.js
  ├ □ Add recent transactions feed with status indicators
  ├ □ Create payment methods breakdown section
  ├ □ Style with Tailwind CSS (gradients, shadows, hover effects)
  ├ □ Add responsive grid layout for different screen sizes
  ├ □ Implement smooth animations and transitions
  ├ □ Create quick action buttons for common tasks
  └ □ Add search functionality and notification system
```

## Implementation Details

### Core Components

1. **TaskGenerator Service** (`packages/cli/src/ui/services/taskGenerator.ts`)
   - **Uses the actual LLM** to generate contextual tasks
   - Sends specialized prompts to the LLM for task generation
   - Parses LLM responses to extract structured task lists
   - Manages task state and completion tracking
   - Falls back to basic parsing if LLM is unavailable

2. **TasksMessage Component** (`packages/cli/src/ui/components/messages/TasksMessage.tsx`)
   - Renders the task list in the CLI interface
   - Shows checkboxes and task completion status
   - Uses consistent styling with the rest of the UI

3. **useTaskGeneration Hook** (`packages/cli/src/ui/hooks/useTaskGeneration.ts`)
   - Manages task generation lifecycle
   - Integrates with the history management system
   - Provides methods for updating task progress

### Integration Points

The feature is integrated into the main chat flow in `useGeminiStream.ts`:
- Tasks are generated immediately after user messages are added to history
- Works for both normal queries and @-commands
- Runs automatically without user intervention

### Type Definitions

New types added to support the feature:
- `TaskItem`: Individual task with ID, text, and completion status
- `HistoryItemTasks`: History item type for task lists
- `MessageType.TASKS`: Enum value for task message type

## Internal LLM Feature

This is an **internal feature for the LLM only** - it's designed to help the AI assistant:
- Track progress on complex multi-step requests
- Organize work into logical subtasks
- Provide structure for handling user requests
- Maintain context across different parts of a solution

The feature is not user-facing in terms of interaction - users cannot manually check off tasks or modify them. The tasks serve as an internal organizational tool for the LLM to better manage and track its work.

## Future Enhancements

Potential improvements that could be added:
1. Task persistence across sessions
2. Progress indicators showing completion percentage
3. More sophisticated task generation based on codebase context
4. Integration with actual work completion tracking
5. Hierarchical subtasks for complex requests

## Configuration

The feature is enabled by default and requires no configuration. It automatically activates for all user prompts that are sent to the LLM for processing.
