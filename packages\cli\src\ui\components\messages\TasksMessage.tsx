/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
import { TaskItem } from '../../types.js';

interface TasksMessageProps {
  title: string;
  tasks: TaskItem[];
}

export const TasksMessage: React.FC<TasksMessageProps> = ({ title, tasks }) => {
  const renderTask = (task: TaskItem, index: number) => {
    const checkbox = task.completed ? '✓' : '□';
    const textColor = task.completed ? Colors.AccentGreen : Colors.Gray;

    return (
      <Box key={task.id} flexDirection="row" marginLeft={2}>
        <Text color={task.completed ? Colors.AccentGreen : Colors.Gray}>
          {checkbox}
        </Text>
        <Text color={textColor} dimColor={task.completed}>
          {task.text}
        </Text>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" marginTop={1}>
      <Box flexDirection="row">
        <Text color={Colors.AccentPurple}>● </Text>
        <Text color={Colors.AccentPurple} bold>
          {title}
        </Text>
      </Box>
      <Box flexDirection="column" marginLeft={2}>
        {tasks.map((task, index) => renderTask(task, index))}
      </Box>
    </Box>
  );
};
