/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useCallback, useRef } from 'react';
import { TaskGenerator } from '../services/taskGenerator.js';
import { TaskItem, HistoryItemTasks } from '../types.js';
import { UseHistoryManagerReturn } from './useHistoryManager.js';

interface UseTaskGenerationReturn {
  generateAndAddTasks: (userPrompt: string, userMessageTimestamp: number) => void;
  updateTaskProgress: (taskId: string, completed: boolean) => void;
  completeCurrentTask: () => void;
}

/**
 * Hook for managing automatic task generation from user prompts.
 * This is an internal LLM feature to track progress, similar to Claude Code.
 */
export const useTaskGeneration = (
  addItem: UseHistoryManagerReturn['addItem']
): UseTaskGenerationReturn => {
  const taskGenerator = TaskGenerator.getInstance();
  const currentTasksRef = useRef<TaskItem[]>([]);
  const currentTasksIdRef = useRef<number | null>(null);

  const generateAndAddTasks = useCallback(
    (userPrompt: string, userMessageTimestamp: number) => {
      // Generate tasks based on the user prompt
      const { title, tasks } = taskGenerator.generateTasksFromPrompt(userPrompt);
      
      // Store current tasks for updates
      currentTasksRef.current = tasks;
      
      // Add tasks to history
      const taskItemId = addItem(
        {
          type: 'tasks',
          title,
          tasks,
        } as HistoryItemTasks,
        userMessageTimestamp
      );
      
      currentTasksIdRef.current = taskItemId;
    },
    [addItem, taskGenerator]
  );

  const updateTaskProgress = useCallback(
    (taskId: string, completed: boolean) => {
      if (currentTasksRef.current.length === 0) return;
      
      currentTasksRef.current = taskGenerator.updateTaskStatus(
        currentTasksRef.current,
        taskId,
        completed
      );
      
      // Note: In a real implementation, you might want to update the history item
      // For now, we just update the internal state
    },
    [taskGenerator]
  );

  const completeCurrentTask = useCallback(() => {
    if (currentTasksRef.current.length === 0) return;
    
    const { updatedTasks, nextTask } = taskGenerator.completeCurrentTask(
      currentTasksRef.current
    );
    
    currentTasksRef.current = updatedTasks;
    
    // Note: In a real implementation, you might want to update the history item
    // and possibly show progress indicators
  }, [taskGenerator]);

  return {
    generateAndAddTasks,
    updateTaskProgress,
    completeCurrentTask,
  };
};
