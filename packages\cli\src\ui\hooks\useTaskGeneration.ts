/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useCallback, useRef } from 'react';
import { TaskGenerator } from '../services/taskGenerator.js';
import { TaskItem, HistoryItemTasks } from '../types.js';
import { UseHistoryManagerReturn } from './useHistoryManager.js';
import { GeminiClient } from 'fumbly-cli-core';

interface UseTaskGenerationReturn {
  generateAndAddTasks: (userPrompt: string, userMessageTimestamp: number) => Promise<void>;
  updateTaskProgress: (taskId: string, completed: boolean) => void;
  completeCurrentTask: () => void;
}

/**
 * Hook for managing automatic task generation from user prompts.
 * This is an internal LLM feature to track progress, similar to Claude Code.
 */
export const useTaskGeneration = (
  addItem: UseHistoryManagerReturn['addItem'],
  geminiClient?: GeminiClient
): UseTaskGenerationReturn => {
  const taskGenerator = TaskGenerator.getInstance();
  const currentTasksRef = useRef<TaskItem[]>([]);
  const currentTasksIdRef = useRef<number | null>(null);

  // Set the Gemini client for LLM-powered task generation
  if (geminiClient) {
    taskGenerator.setGeminiClient(geminiClient);
  }

  const generateAndAddTasks = useCallback(
    async (userPrompt: string, userMessageTimestamp: number) => {
      try {
        // Generate tasks based on the user prompt using LLM
        const { title, tasks } = await taskGenerator.generateTasksFromPrompt(userPrompt);

        // Store current tasks for updates
        currentTasksRef.current = tasks;

        // Add tasks to history
        const taskItemId = addItem(
          {
            type: 'tasks',
            title,
            tasks,
          } as HistoryItemTasks,
          userMessageTimestamp
        );

        currentTasksIdRef.current = taskItemId;
      } catch (error) {
        console.warn('Failed to generate tasks:', error);
        // Don't add tasks if generation fails
      }
    },
    [addItem, taskGenerator]
  );

  const updateTaskProgress = useCallback(
    (taskId: string, completed: boolean) => {
      if (currentTasksRef.current.length === 0) return;
      
      currentTasksRef.current = taskGenerator.updateTaskStatus(
        currentTasksRef.current,
        taskId,
        completed
      );
      
      // Note: In a real implementation, you might want to update the history item
      // For now, we just update the internal state
    },
    [taskGenerator]
  );

  const completeCurrentTask = useCallback(() => {
    if (currentTasksRef.current.length === 0) return;
    
    const { updatedTasks, nextTask } = taskGenerator.completeCurrentTask(
      currentTasksRef.current
    );
    
    currentTasksRef.current = updatedTasks;
    
    // Note: In a real implementation, you might want to update the history item
    // and possibly show progress indicators
  }, [taskGenerator]);

  return {
    generateAndAddTasks,
    updateTaskProgress,
    completeCurrentTask,
  };
};
